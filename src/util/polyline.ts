import * as L from 'leaflet';
import * as ui from '@/util/ui';

function pointDist(a: <PERSON><PERSON>, b: <PERSON>.<PERSON>t<PERSON>ng): number {
  let dx = a.lng - b.lng;
  let dy = a.lat - b.lat;
  return Math.sqrt(dx * dx + dy * dy);
}

function pointArrayLength(p: L.LatLng[] | L.LatLng[][] | L.LatLng[][][]): number {
  let dist = 0.0;
  if (p.length == 0) {
    return dist;
  }
  if (p[0] instanceof L.LatLng) {
    for (let i = 1; i < p.length; i++) {
      dist += pointDist(p[i - 1] as <PERSON><PERSON>, p[i] as <PERSON>.<PERSON>t<PERSON>ng);
    }
    return dist;
  }
  for (let i = 0; i < p.length; i++) {
    dist += pointArrayLength(p[i] as L.LatLng[] | L.LatLng[][]);
  }
  return dist;
}

function polyLineLength(layer: L.Polyline) {
  return pointArrayLength(layer.getLatLngs());
}

export function calcLayerLength(layer: L.Marker | L.Polyline) {
  if (!layer.feature) {
    return;
  }
  layer.feature.properties.pathLength = 0;
  if (ui.leafletType(layer) == ui.LeafletType.Polyline) {
    layer.feature.properties.pathLength = polyLineLength(layer as L.Polyline);
  } else {
    layer.feature.properties.length = 0;
  }
  // Tell Popup the length parameter
  let z: any = layer;
  if (z.popup) {
    z.popup.pathLength = layer.feature.properties.pathLength;
  }
}
