import * as L from 'leaflet';

export const DUNGEON = L.icon({
  iconUrl: '/icons/mapicon_dungeon.svg',
  iconSize: L.point(36, 36),
  className: 'mapicon-Dungeon',
});
export const DUNGEON_DLC = L.icon({
  iconUrl: '/icons/mapicon_dungeon_dlc.svg',
  iconSize: L.point(36, 36),
  className: 'mapicon-Dungeon',
});
export const TOTK_SHRINE = L.icon({
  iconUrl: '/icons/shrine.svg',
  iconSize: L.point(32, 32),
  className: 'mapicon-totk-Shrine'
});
export const TOTK_SHRINE_CAVE = L.icon({
  iconUrl: '/icons/shrine_cave.svg',
  iconSize: L.point(32, 32),
  className: 'mapicon-totk-Shrine'
});
export const TOTK_TOWER = L.icon({
  iconUrl: '/icons/tower.svg',
  iconSize: L.point(32, 32),
  className: 'mapicon-totk-Tower'
});
export const TOTK_TEAR = L.icon({
  iconUrl: '/icons/tear.svg',
  iconSize: L.point(24, 24),
  className: 'mapicon-totk-Tear'
});
export const TOTK_LIGHTROOT = L.icon({
  iconUrl: '/icons/lightroot.svg',
  iconSize: L.point(32, 32),
  className: 'mapicon-totk-Lightroot'
});
export const BARGAINER = L.icon({
  iconUrl: '/icons/bargainer_statue.svg',
  iconSize: L.point(20, 20),
});
export const DISPENSER = L.icon({
  iconUrl: '/icons/dispenser.svg',
  iconSize: L.point(20, 20),
});
export const STAR = L.icon({
  iconUrl: '/icons/star.svg',
  iconSize: L.point(20, 20),
});
export const BATTERY = L.icon({
  iconUrl: '/icons/battery.svg',
  iconSize: L.point(20, 20),
});
export const DRINK = L.icon({
  iconUrl: '/icons/drink.svg',
  iconSize: L.point(20, 20),
});
export const SWORD = L.icon({
  iconUrl: '/icons/sword.svg',
  iconSize: L.point(20, 20),
});

export const VILLAGE = L.icon({
  iconUrl: '/icons/mapicon_village.svg',
  iconSize: L.point(32, 32),
  tooltipAnchor: [0, 20],
});
export const CHECKPOINT = L.icon({
  iconUrl: '/icons/mapicon_checkpoint.svg',
  iconSize: L.point(26, 26),
  tooltipAnchor: [0, 18],
});
export const HATAGO = L.icon({
  iconUrl: '/icons/mapicon_hatago.svg',
  iconSize: L.point(32, 32),
  tooltipAnchor: [0, 20],
});
export const CASTLE = L.icon({
  iconUrl: '/icons/mapicon_castle.svg',
  iconSize: L.point(32, 32),
  tooltipAnchor: [0, 20],
});
export const CAVE = L.icon({
  iconUrl: '/icons/cave.png',
  iconSize: L.point(20, 20),
  tooltipAnchor: [0, 18],
});
export const WELL = L.icon({
  iconUrl: '/icons/well.svg',
  iconSize: L.point(20, 20),
  tooltipAnchor: [0, 18],
});
export const CHASM = L.icon({
  iconUrl: '/icons/chasm.png',
  iconSize: L.point(20, 20),
  tooltipAnchor: [0, 18],
});


export const TOWER = L.icon({
  iconUrl: '/icons/mapicon_tower.svg',
  iconSize: L.point(40, 40),
  className: 'mapicon-Tower',
});
export const LABO = L.icon({
  iconUrl: '/icons/mapicon_labo.svg',
  iconSize: L.point(32, 32),
});


export const SHOP_BOUGU = L.icon({
  iconUrl: '/icons/mapicon_shop_bougu.svg',
  iconSize: L.point(32, 32),
});
export const SHOP_COLOR = L.icon({
  iconUrl: '/icons/mapicon_shop_color.svg',
  iconSize: L.point(32, 32),
});
export const SHOP_JEWEL = L.icon({
  iconUrl: '/icons/mapicon_shop_jewel.svg',
  iconSize: L.point(32, 32),
});
export const SHOP_YADOYA = L.icon({
  iconUrl: '/icons/mapicon_shop_yadoya.svg',
  iconSize: L.point(32, 32),
});
export const SHOP_YOROZU = L.icon({
  iconUrl: '/icons/mapicon_shop_yorozu.svg',
  iconSize: L.point(32, 32),
});


export const KOROK = L.icon({
  iconUrl: '/icons/mapicon_korok.png',
  iconSize: L.point(20, 20),
});
