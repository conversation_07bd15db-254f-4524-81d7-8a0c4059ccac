@font-face {
  font-family: <PERSON><PERSON><PERSON>;
  src: url('./assets/fonts/Sheikah-Complete.ttf');
}

@font-face {
  font-family: Calamity;
  font-style: italic;
  src: url('./assets/fonts/Calamity-Regular.otf');
}

@font-face {
  font-family: CalamityB;
  font-weight: bold;
  font-style: italic;
  src: url('./assets/fonts/Calamity-Bold.otf');
}

html, body {
  height: 100%;
}

body {
  font-family: Calamity, Roboto, sans-serif;
  font-size: 16px;
  background-color: #000000;
  background-image: url("assets/background.png");
  background-attachment: fixed;
  box-sizing: border-box;
}

a, a:active, a:visited {
  color: #29d1fc;
}

code {
  color: #ffd800;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity .2s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.pane {
  background-color: #002645b0;
  color: #29abfc;
  text-shadow: 0 0 30px #29d1fc7f;
}

.pane-header {
  background-color: #01151ee0;
}

.pane-content {
  padding: 15px;
}

.form-control, .form-control:focus, .custom-control-label::before {
  color: white;
  background-color: #01151fe0;
  border: 1px solid #01151fe0;
}

.form-control::placeholder {
  color: #7a7a7a;
}

/* https://stackoverflow.com/a/49852634 */
.leaflet-tooltip.korok {
  background-color: #ffffff00;
  border: 0px solid #ffffff00;
  box-shadow: 0 1px 3px #ffffff00;
  color: black;
}
.leaflet-tooltip.ulri {
  text-shadow:    1px 1px #FBFE53 ,  1px -1px #FBFE53 ,
                 -1px 1px #FBFE53 , -1px -1px #FBFE53 ;
}
.leaflet-tooltip.hyrulefield {
  text-shadow:    1px 1px #E1FF8E ,  1px -1px #E1FF8E ,
                 -1px 1px #E1FF8E , -1px -1px #E1FF8E ;
}
.leaflet-tooltip.eldin {
  text-shadow:    1px 1px #F29F5D ,  1px -1px #F29F5D ,
                 -1px 1px #F29F5D , -1px -1px #F29F5D ;
}
.leaflet-tooltip.sahasra {
  text-shadow:    1px 1px #CB6987 ,  1px -1px #CB6987 ,
                 -1px 1px #CB6987 , -1px -1px #CB6987 ;
}
.leaflet-tooltip.rabella {
  text-shadow:    1px 1px #5FBD52 ,  1px -1px #5FBD52 ,
                 -1px 1px #5FBD52 , -1px -1px #5FBD52 ;
}
.leaflet-tooltip.gerudohigh {
  text-shadow:    1px 1px #8ACBE0 ,  1px -1px #8ACBE0 ,
                 -1px 1px #8ACBE0 , -1px -1px #8ACBE0 ;
}
.leaflet-tooltip.rospro {
  text-shadow:    1px 1px  #7FD0E0,  1px -1px  #7FD0E0,
                 -1px 1px  #7FD0E0, -1px -1px  #7FD0E0;
}
.leaflet-tooltip.sky {
  text-shadow:    1px 1px  #7FD0E0,  1px -1px  #7FD0E0,
                 -1px 1px  #7FD0E0, -1px -1px  #7FD0E0;
}
.leaflet-tooltip.greatskyisland {
  text-shadow:    1px 1px #efef9e ,  1px -1px #efef9e ,
                 -1px 1px #efef9e , -1px -1px #efef9e ;
}
.leaflet-tooltip.thyphlo {
  text-shadow:    1px 1px #6ACF3D ,  1px -1px #6ACF3D ,
                 -1px 1px #6ACF3D , -1px -1px #6ACF3D ;
}
.leaflet-tooltip.popla {
  text-shadow:    1px 1px #7AA2F8 ,  1px -1px #7AA2F8 ,
                 -1px 1px #7AA2F8 , -1px -1px #7AA2F8 ;
}
.leaflet-tooltip.lanayru {
  text-shadow:    1px 1px #E99E37 ,  1px -1px #E99E37 ,
                 -1px 1px #E99E37 , -1px -1px #E99E37 ;
}
.leaflet-tooltip.plateau {
  text-shadow:    1px 1px #9DCCC3 ,  1px -1px #9DCCC3 ,
                 -1px 1px #9DCCC3 , -1px -1px #9DCCC3 ;
}
.leaflet-tooltip.xhyrulefield {
  text-shadow:    1px 1px  #BD6681,  1px -1px  #BD6681,
                 -1px 1px  #BD6681, -1px -1px  #BD6681;
}
.leaflet-tooltip.tabantha {
  text-shadow:    1px 1px  #5CBC4F,  1px -1px  #5CBC4F,
                 -1px 1px  #5CBC4F, -1px -1px  #5CBC4F;
}
.leaflet-tooltip.gerudo {
  text-shadow:    1px 1px #F2A33A ,  1px -1px #F2A33A ,
                 -1px 1px #F2A33A , -1px -1px #F2A33A ;
}
.leaflet-tooltip.lookout {
  text-shadow:    1px 1px #B2653F ,  1px -1px #B2653F ,
                 -1px 1px #B2653F , -1px -1px #B2653F ;
}
.leaflet-tooltip.zorana {
  text-shadow:    1px 1px #C6B1FF ,  1px -1px #C6B1FF ,
                 -1px 1px #C6B1FF , -1px -1px #C6B1FF ;
}
.leaflet-tooltip.lindor {
  text-shadow:    1px 1px #e08f7f ,  1px -1px #e08f7f ,
                 -1px 1px #e08f7f , -1px -1px #e08f7f ;
}
.leaflet-tooltip.pikida {
  text-shadow:    1px 1px #efef9e ,  1px -1px #efef9e ,
                 -1px 1px #efef9e , -1px -1px #efef9e ;
}


.colorscale-label {
    position: absolute;
    display: inline;
    color: white;
    transform: translateX(-50%);
}
.colorscale-labelbox {
    width: 100%;
    min-height: 1.3em;
    flex: 0 1 100%;
}
.colorscale {
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    align-items: flex-start;
    align-content: flex-start;
}
.colorscale-bar {
    height: 10px;
    min-height: 10px;
    width: calc(200px - 1em);
    left: 0;
    display: block;
    flex: 0 1 100%;
}
.colorscale-title {
    flex: 0 1 100%;
    width: 100%;
    text-align: center;
    color: white;
    margin-bottom: 1px;
}

.colorscale:hover {
    border: 0px solid fuchsia;
}

.colorscale-picker-title {
    color: white;
    text-align: center;
}

.colorscale-picker {
    position: absolute;
    left: 90%;
    top: 0px;
    display: none;
    background: rgba(255,255,255,0.3);
    padding: 2px;
    border: 1px solid #333;
    border-radius: 3px;
    transform: translateY(-80%);
}

.colorscale:hover .colorscale-picker {
    display: block;
}

.colorscale-picker-sample:hover {
    outline: 1px solid white;
}
