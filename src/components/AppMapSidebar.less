.leaflet-sidebar-left {
  left: 0;
}

.leaflet-sidebar-right {
  right: 0;
  .leaflet-sidebar-header {
    padding-left: 20px;
  }
  .leaflet-sidebar-close {
    left: auto;
    right: 0;
  }
}

#sidebar {
  box-sizing: content-box;
  font-family: Calamity;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  z-index: 1001;
  top: 0;
  bottom: 0;
  border: 0;
  border-radius: 0;

  .form-control {
    background-color: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    line-height: 3;
  }
  .form-control::placeholder {
    color: #aaa;
  }
  hr {
    border-color: rgba(255, 255, 255, 0.2);
  }
  .leaflet-sidebar-content, .leaflet-sidebar-tabs {
    background: #002d40e0;
  }
  .leaflet-sidebar-tabs > li, .leaflet-sidebar-tabs > ul > li {
    color: rgba(255, 255, 255, 0.8);
  }
  .leaflet-sidebar-tabs > li:hover, .leaflet-sidebar-tabs > ul > li:hover {
    background: rgba(0, 0, 0, 0.3) !important;
    color: white;
  }
  .leaflet-sidebar-tabs > li.active, .leaflet-sidebar-tabs > ul > li.active {
    background-color: inherit;
    color: #29abfc;
  }
  .leaflet-sidebar-tabs > li.disabled, .leaflet-sidebar-tabs > ul > li.disabled {
    color: rgba(255, 255, 255, 0.3);
    pointer-events: none;
  }
  .leaflet-sidebar-content {
    color: white;
    text-shadow: 0 0 20px #ffffff9f;
    padding-top: 0.5em;

    a:not(.btn), a:not(.btn):active, a:not(.btn):visited, .btn-link {
      color: #29d1fc;
      text-decoration: none;
    }

    .btn-link:hover {
      color: #8ce7ff;
    }

    .dropdown-menu {
      font-size: 14px;
    }

    .dropdown-menu a {
      color: #212529 !important;
      padding: 0.25rem 1rem;
    }
  }
  .leaflet-sidebar-header {
    margin-bottom: 0.5em;
    background-color: inherit;
    text-shadow: 0 0 30px #ffffffbf;
    font-size: 20px;
    font-family: CalamityB;
  }
  .leaflet-sidebar-close {
    padding-top: 7px;
    font-size: 18px;
  }
}

// Modified from https://codepen.io/Spemer/pen/baoKPK
.leaflet-sidebar-content::-webkit-scrollbar {
  background-color: transparent;
  width: 8px;
}

.leaflet-sidebar-content::-webkit-scrollbar-thumb {
  background-color:#babac0;
  border-radius:16px;
}

.leaflet-sidebar-content::-webkit-scrollbar-thumb:hover {
  background-color:#a0a0a5;
  border:4px solid #f4f4f4
}
.leaflet-sidebar-content::-webkit-scrollbar-button {display:none}

.subsection-heading {
  font-family: CalamityB;
  font-size: 16px;
}

summary .subsection-heading {
  display: inline-block;
}

.location-title {
  font-size: 22px !important;
  color: #b7f1ff;
  text-shadow: 0 0 20px #3aa0ff, 0 0 20px #3aa0ff, 0 0 20px #3aa0ff !important;
  margin-top: 10px;
  margin-bottom: 3px !important;
  white-space: nowrap;
  span {
    padding-bottom: 7px;
    border-bottom: 1px solid #b7f1ff3f;
  }
}

.location-sub {
  font-size: 15px !important;
  color: #b7f1ff;
  text-shadow: 0 0 16px #39c7ff, 0 0 16px #39c7ff, 0 0 16px #39c7ff !important;
  font-size: 14px;
}

/* Search */
.search-groups {
  margin-bottom: 5px;
  font-size: 95%;
}
