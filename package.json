{"name": "botw-objmap", "version": "0.1.0", "private": true, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "scripts": {"serve": "env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve --host localhost", "build": "env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build", "lint": "env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service lint"}, "dependencies": {"@fortawesome/fontawesome-free": "^5.13.0", "@types/leaflet-mouse-position": "^1.2.1", "bootstrap": "^4.5.0", "bootstrap-vue": "^2.15.0", "git-describe": "^4.1.1", "idb": "^7.1.1", "leaflet": "^1.9.4", "leaflet-contextmenu": "^1.4.0", "leaflet-defaulticon-compatibility": "^0.1.1", "leaflet-draw": "^1.0.4", "leaflet-hotline": "github:savage13/Leaflet.hotline", "leaflet-mouse-position": "^1.2.0", "leaflet-path-transform": "^1.1.3", "leaflet-rastercoords": "^1.0.3", "leaflet-sidebar-v2": "^3.2.3", "leaflet.control.opacity": "^1.6.0", "leaflet.markercluster": "^1.4.1", "lodash": "^4.17.21", "terser": "^4.8.1", "vue": "^2.6.11", "vue-class-component": "^7.2.3", "vue-property-decorator": "^7.0.0", "vue-router": "^3.2.0", "vue-virtual-scroller": "^1.1.2", "vuedraggable": "^2.24.3"}, "devDependencies": {"@types/leaflet": "^1.5.21", "@types/leaflet-draw": "^0.4.14", "@types/leaflet.markercluster": "^1.4.3", "@types/lodash": "^4.14.168", "@vue/cli-plugin-babel": "^3.12.1", "@vue/cli-plugin-eslint": "^3.12.1", "@vue/cli-plugin-typescript": "^3.12.1", "@vue/cli-service": "^3.12.1", "@vue/eslint-config-typescript": "^4.0.0", "babel-eslint": "^10.1.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.2.3", "less": "^3.13.1", "less-loader": "^4.1.0", "typescript": "^3.9.7", "vue-template-compiler": "^2.6.11"}}