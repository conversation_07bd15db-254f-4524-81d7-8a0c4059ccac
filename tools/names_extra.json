{"Animal_Crow_Swarm": "Mountain Crow", "Animal_Dog_B": "<PERSON><PERSON><PERSON>ever", "Animal_Dog_C": "<PERSON><PERSON><PERSON>ever", "Animal_Donkey_B": "<PERSON><PERSON>", "Animal_Donkey_B_MOTD": "<PERSON><PERSON>", "Animal_Donkey_C": "<PERSON><PERSON>", "Animal_Goat_B": "White Goat", "Animal_Gull_Swarm": "Seagull", "Animal_Horse_A_00L": "Giant Horse", "Animal_Horse_A_01L": "Giant White Stallion", "Animal_Horse_A_Epona": "Horse", "Animal_Horse_A_SpPattern": "Horse", "Animal_Horse_A_Zelda": "White Horse", "Animal_Insect_NA": "Winterwing Butterfly", "Animal_LittleBird_G": "<PERSON> Sparrow", "Animal_Pigeon_B_Swarm": "<PERSON> Pigeon", "Animal_Pigeon_Swarm": "Wood Pigeon", "Animal_RupeeRabbit_FromCaveMaster": "Blupee", "Animal_RupeeRabbit_NushiShop": "Blupee", "Animal_SkyGull_Swarm": "Cloud Seagull", "Animal_SkyLittleBird_F": "Golden Sparrow", "Animal_SunazarashiSP_A": "Sand Seal", "Animal_Sunazarashi_C": "Sand Seal", "Armor_001_Head_B": "<PERSON><PERSON><PERSON>", "Armor_002_Head_B": "<PERSON><PERSON><PERSON>", "Armor_003_Head_B": "<PERSON><PERSON><PERSON>", "Armor_004_Head_B": "<PERSON><PERSON><PERSON>", "Armor_012_Head_B": "Stealth Mask", "Armor_015_Head_B": "<PERSON><PERSON><PERSON>", "Armor_022_Head_B": "Bokoblin Mask", "Armor_042_Head_B": "Stealth Mask", "Armor_077_Head_B": "Stealth Mask", "Armor_078_Head_B": "Stealth Mask", "Armor_079_Head_B": "Stealth Mask", "Armor_1125_Head_B": "Horriblin Mask", "Armor_1152_Head_B": "<PERSON><PERSON><PERSON>", "Armor_1153_Head_B": "<PERSON><PERSON><PERSON>", "Armor_1154_Head_B": "<PERSON><PERSON><PERSON>", "Armor_1155_Head_B": "<PERSON><PERSON><PERSON>", "Armor_1156_Head_B": "<PERSON><PERSON><PERSON>", "Armor_177_Head_B": "<PERSON><PERSON>'s Hood", "Armor_178_Head_B": "<PERSON><PERSON>'s <PERSON><PERSON><PERSON>", "Armor_220_Head_B": "<PERSON><PERSON>'s Mask", "Armor_221_Head_B": "<PERSON><PERSON>'s Mask", "Armor_222_Head_B": "<PERSON><PERSON>'s Mask", "Armor_223_Head_B": "<PERSON><PERSON>'s Mask", "Armor_224_Head_B": "<PERSON><PERSON>'s Mask", "AsbObj_Assassin_BaloonkeyPlate_01": "Scrap", "AsbObj_Assassin_Raft_01": "Raft", "AsbObj_BalloonEnvelopeBase_A_01": "Basket", "AsbObj_BalloonEnvelopeBase_A_03": "Basket", "AsbObj_BalloonEnvelopeBase_Cloth_A_01": "Basket", "AsbObj_BalloonEnvelopeBase_Zonau_A_01": "Basket", "AsbObj_HyliaWoodRuinShelter_Wood_B_01": "Board", "AsbObj_HyliaWoodRuinShelter_Wood_C_01": "Board", "AsbObj_HyliaWoodRuinShelter_Wood_C_02": "Board", "AsbObj_Icicle_A_01": "I<PERSON><PERSON>", "AsbObj_Icicle_A_02": "I<PERSON><PERSON>", "AsbObj_MetalPole_A_LL_01": "Iron Pole", "AsbObj_MetalRectangle_A_LL_01": "Metal Plate", "AsbObj_MetalRectangle_A_M_01": "Metal Plate", "AsbObj_MetalSquare_A_M_01": "Metal Plate", "AsbObj_RockParts_C_L_01": "Boulder", "AsbObj_RockParts_C_S_01": "Rock", "AsbObj_RopewayArm_A_01": "Hook", "AsbObj_RopewayArm_A_03": "Hook", "AsbObj_SharpRock_A_S_01": "Stone", "AsbObj_SpikeDynamic_A_01": "Spikes", "AsbObj_StoneLightSquare_A_M_01": "<PERSON><PERSON><PERSON>", "AsbObj_StoneLightSquare_A_M_02": "<PERSON><PERSON><PERSON>", "AsbObj_StoneRectangle_B_LL_03": "<PERSON> Slab", "AsbObj_StoneRectangle_B_M_01": "<PERSON> Slab", "AsbObj_StoneSquare_B_M_01": "<PERSON> Slab", "AsbObj_WhiteWoodRectangle_A_LL_01": "Board", "AsbObj_WhiteWoodRectangle_A_M_01": "Board", "AsbObj_WhiteWoodSquare_A_M_01": "Board", "AsbObj_WhiteWoodStick_A_LL_01": "Lumber", "AsbObj_WoodRectangle_A_M_01": "Board", "AsbObj_WoodStableHostelShed_A_01": "Board", "AsbObj_WoodWheel_A_02": "Wheel", "AsbObj_WoodWheel_A_03": "Wheel", "AssassinTextNPC_Tribune": "Yiga Footsoldier", "Barrel_CDungeon": "Barrel", "Barrel_SkyObj": "Barrel", "Barrel_SkyObjOld_A_01": "Barrel", "BoarMeat": "<PERSON><PERSON>", "BrokenSnowBall": "Snowball", "BrokenSnowBallForAttachmentLarge": "Snowball", "BrokenSnowBallForAttachmentSmall": "Snowball", "Cave_LanayruMountain_0008_StoneBall": "<PERSON> Ball", "ConfusionFruit_Static": "Muddle Bud", "DgnObj_BoardFloat_A": "Board", "DgnObj_BoardIron_A": "Metal Plate", "DgnObj_BoardIron_E_Cart": "Metal Plate", "DgnObj_BoardLight_A": "<PERSON>", "DgnObj_BoardStone_A": "<PERSON> Slab", "DgnObj_BoardStone_B": "<PERSON> Slab", "DgnObj_BoardWood_A": "Board", "DgnObj_BoxStone_A": "Stone Box", "DgnObj_ElectricPot_A_LL_Act_01": "Pot", "DgnObj_ElectricStoneBoard_A_01": "<PERSON> Slab", "DgnObj_ElectricStoneBoard_A_02": "<PERSON> Slab", "DgnObj_ElectricSwitchCapstan_A_02": "Pole", "DgnObj_Fire_Train_A_01": "Mine Cart", "DgnObj_FloatingWater_A_01": "Water Globule", "DgnObj_FloatingWater_A_01_ForAttachment": "Water Globule", "DgnObj_FloatingWater_A_02": "Water Globule", "DgnObj_FloatingWater_A_02_ForAttachment": "Water Globule", "DgnObj_IceBlock": "Ice", "DgnObj_LargeDungeonWind_Debris_A_01": "<PERSON>", "DgnObj_LargeDungeonWind_Grate_A_01": "<PERSON><PERSON><PERSON>", "DgnObj_MetalBarrel_A": "Barrel", "DgnObj_PropellerAndWind": "Propeller", "DgnObj_SecretBox_A": "Stone Box", "DgnObj_SeesawExtend_A": "Board", "DgnObj_Small_BarrelBomb_A_01": "Bomb Barrel", "DgnObj_Small_BarrelBomb_A_02": "Bomb Barrel", "DgnObj_Small_BoardIron_A_04": "Metal Plate", "DgnObj_Small_BoardNet_A_02": "<PERSON><PERSON><PERSON>", "DgnObj_Small_BoardNet_A_04": "<PERSON>", "DgnObj_Small_BoardNet_A_05": "<PERSON>", "DgnObj_Small_BoardStone_A_02": "<PERSON> Slab", "DgnObj_Small_BoardStone_A_03": "<PERSON> Slab", "DgnObj_Small_BoardStone_A_04": "<PERSON> Slab", "DgnObj_Small_BoardStone_A_06": "<PERSON> Slab", "DgnObj_Small_BoardStone_A_07": "<PERSON> Slab", "DgnObj_Small_BoardWood_B_02": "Board", "DgnObj_Small_BoardWood_B_03": "Board", "DgnObj_Small_BoardWood_B_04": "Board", "DgnObj_Small_BoardWood_B_05": "Board", "DgnObj_Small_BoxIron_B_03": "Iron Box", "DgnObj_Small_BoxIron_B_04": "Iron Box", "DgnObj_Small_BoxStone_A_02": "Stone Box", "DgnObj_Small_BoxStone_A_03": "Stone Box", "DgnObj_Small_BoxStone_A_04": "Stone Box", "DgnObj_Small_BoxWood_A_02": "Wooden Box", "DgnObj_Small_Grate_A_01": "<PERSON><PERSON><PERSON>", "DgnObj_Small_IronBall_03": "Iron Ball", "DgnObj_Small_IronBall_04": "Iron Ball", "DgnObj_Small_IronBall_05": "Iron Ball", "DgnObj_Small_IronStick_A_01": "Iron Pole", "DgnObj_Small_PuzzleBlock_A_01": "Block", "DgnObj_Small_PuzzleBlock_A_02": "Block", "DgnObj_Small_PuzzleBlock_A_03": "Block", "DgnObj_Small_PuzzleRing_A_01": "<PERSON> Slab", "DgnObj_Small_PuzzleRing_B_01": "Block", "DgnObj_Small_RaftWood_A_01": "Raft", "DgnObj_Small_SpikeDynamic_A_01": "Spikes", "DgnObj_Small_StoneBallL_02": "<PERSON> Ball", "DgnObj_Small_WaterWheel_A_02": "Board", "DgnObj_Small_WoodPole_A_02": "Log", "DgnObj_Small_floatFortParts_A_01": "Raft", "DgnObj_SpikeBallWood_A": "Spikes", "DgnObj_SpikeBall_A": "Spiked Iron Ball", "DgnObj_SpikeBall_B": "Spiked Iron Ball", "DgnObj_StoneBall_NoFire": "Flame Ball", "DgnObj_StoneBarrel_A": "Barrel", "DgnObj_Train_A_01": "Mine Cart", "DgnObj_Train_A_02": "Mine Cart", "DgnObj_WoodPole_A": "Log", "Dm_Enemy_Bokoblin_Boss_Senior": "Black Boss Bokoblin", "Dm_Enemy_Bokoblin_Junior": "Bokoblin", "Dm_Enemy_Bokoblin_Middle": "Blue Bokoblin", "Dm_Enemy_Bokoblin_Senior": "Black Bokoblin", "Dm_Npc_Raul_Soul": "Rauru", "Dm_Npc_Raumi_Ghost": "Mineru", "Dm_Npc_Zelda_AncientHyrule": "<PERSON><PERSON><PERSON>", "Dm_Npc_Zelda_Catch": "<PERSON><PERSON><PERSON>", "Dm_Sage_Soul_Gerudo": "Riju", "Dm_Sage_Soul_Goron": "<PERSON><PERSON><PERSON>", "Dm_Sage_Soul_Rito": "<PERSON><PERSON>", "Dm_Sage_Soul_Zora": "Sidon", "Dm_TBox_Field_Iron": "Treasure Chest", "Drake_Icicle": "I<PERSON><PERSON>", "Drake_IcicleForAttachment": "I<PERSON><PERSON>", "DummyHorse": "Horse", "Enemy_Assassin_Junior_Practice": "Yiga Footsoldier", "Enemy_Assassin_Junior_Shooter_Subordinates": "Yiga Footsoldier", "Enemy_Assassin_Shooter_Junior_Practice": "Yiga Footsoldier", "Enemy_Bokoblin_Bone_Junior_Head": "Stalkoblin", "Enemy_Bokoblin_Bone_Junior_Head_AllDay": "Stalkoblin", "Enemy_Chuchu_Electric_Middle_Army": "Electric Chuchu", "Enemy_Chuchu_Fire_Middle_Army": "Fire Chuchu", "Enemy_Chuchu_Ice_Middle_Army": "<PERSON>chu", "Enemy_Dragon_Light_002": "Light Dragon", "Enemy_DungeonBoss_Zora_Bullet_Shark": "Mucktorok", "Enemy_DungeonBoss_Zora_Bullet_SharkFin": "Mucktorok", "Enemy_Keese_Cadaver": "<PERSON><PERSON>", "Enemy_Keese_Ice_Army": "<PERSON> Keese", "Enemy_Keese_Swarm": "<PERSON><PERSON>", "Enemy_Keese_Swarm_AllDay": "<PERSON><PERSON>", "Enemy_Kohga_GolemRider": "Master <PERSON><PERSON><PERSON>", "Enemy_Lizalfos_Bone_Junior_Head": "Stalizalfos", "Enemy_Lynel_Boss": "<PERSON><PERSON><PERSON>", "Enemy_Lynel_Boss_Dark": "Silver Lynel", "Enemy_Lynel_Boss_Middle": "<PERSON>-<PERSON><PERSON>", "Enemy_Lynel_Boss_Senior": "White-<PERSON><PERSON>", "Enemy_MiddleBoss_Goron_WeakPoint": "Moragia", "Enemy_MiddleBoss_Goron_WeakPoint_A": "Moragia", "Enemy_MiddleBoss_Goron_WeakPoint_B": "Moragia", "Enemy_MiddleBoss_Goron_WeakPoint_C": "Moragia", "Enemy_Moriblin_Bone_Junior_Head": "Stalmoblin", "Enemy_Moriblin_Bone_Junior_Head_AllDay": "Stalmoblin", "Enemy_Toby_Junior_Terminal": "Aerocuda", "Enemy_Treant_Bee": "Evermean", "Enemy_Treant_D": "Evermean", "Enemy_Treant_E": "Evermean", "Enemy_Zombie_Boss_Junior_Defense": "<PERSON><PERSON> Gibdo", "Enemy_Zombie_Boss_Junior_ForDungeonBossGerudoBattle": "<PERSON><PERSON> Gibdo", "Enemy_Zombie_Junior_ForDungeonBossGerudoBattle": "<PERSON><PERSON><PERSON>", "Enemy_Zonau_Golem_Junior_ForFirstSmallDungeon": "Captain Construct I", "FireWoodFromBundle": "<PERSON>", "FireWoodFromBundleBurnedByBoostedFire": "<PERSON>", "FireWoodLong": "<PERSON>", "FldObj_DeathMtArtifactTrain_A_02": "Mine Cart", "FldObj_DeathMtArtifactTrain_A_03": "Mine Cart", "FldObj_DeathMtArtifactTrain_A_03_BP": "Mine Cart", "FldObj_DeathMtArtifactTrain_B_01": "Mine Cart", "FldObj_DeathMtArtifactTrain_B_01_MiniGame": "Mine Cart", "FldObj_DeathMtArtifactTrain_B_02": "Mine Cart", "FldObj_DeathMtArtifactTrain_B_02_BP": "Mine Cart", "FldObj_EnemyLookoutBanana_A_01_Trunk": "Log", "FldObj_FallingRock_A_01": "Boulder", "FldObj_FallingRock_A_02": "Boulder", "FldObj_FallingRock_B_01": "Boulder", "FldObj_FallingRock_B_02": "Boulder", "FldObj_HatenoGateBook_Kakariko": "Calip's Journal", "FldObj_HyliaWoodRuinWell_A_02": "Well", "FldObj_HyliaWoodRuinWell_A_03": "Well", "FldObj_LinkHouse_A_01": "Furnished Square Room", "FldObj_LinkHouse_B_01": "Blessing Room", "FldObj_LinkHouse_C_01": "Bedroom", "FldObj_LinkHouse_D_01": "Weapon Stand Room", "FldObj_LinkHouse_E_01": "Bow Stand Room", "FldObj_LinkHouse_F_01": "Shield Stand Room", "FldObj_LinkHouse_G_01": "Kitchen", "FldObj_LinkHouse_H_01": "Gallery", "FldObj_LinkHouse_I_01": "Outdoor Stairs", "FldObj_LinkHouse_J_01": "Indoor Stairs", "FldObj_LinkHouse_K_01": "Garden Pond", "FldObj_LinkHouse_L_01": "Flower Bed", "FldObj_LinkHouse_M_01": "Paddock", "FldObj_LinkHouse_N_01": "<PERSON><PERSON><PERSON>", "FldObj_LinkHouse_O_01": "Angled Room", "FldObj_LinkHouse_P_01": "Furnished Angled Room", "FldObj_LinkHouse_Q_01": "Study", "FldObj_LinkHouse_R_01": "Square Room", "FldObj_MockUp_Item_Enemy_117": "Fire Keese Eyeball", "FldObj_MockUp_Item_Enemy_155": "Gleeok Thunder Horn", "FldObj_MockUp_Item_Enemy_156": "Gleeok Wing", "FldObj_MockUp_Item_Enemy_215": "White-Maned <PERSON><PERSON><PERSON>", "FldObj_MockUp_Item_Enemy_32": "Hinox Toenail", "FldObj_MockUp_Item_Enemy_42": "Ice-Breath <PERSON><PERSON><PERSON>", "FldObj_PushRockIron_A_M_01": "Iron Lump", "FldObj_PushRock_A_M_01": "Boulder", "FldObj_ScaffoldWood_A_03_Trunk": "Log", "FldObj_SignboardWood_A_01_Trunk": "Signboard", "FldObj_SignboardWood_A_02_Trunk": "Signboard", "FldObj_WisemanStatue_A_06_End": "Winged statue", "FldObj_ZonauFallingParts_B_Block_Fall_A_01": "<PERSON><PERSON><PERSON>", "GameRomHorse01": "Horse", "GameRomHorse01S": "<PERSON><PERSON>", "GameRomHorse02": "Horse", "GameRomHorse03": "Horse", "GameRomHorse04": "Horse", "GameRomHorse05": "Horse", "GameRomHorse06": "Horse", "GameRomHorse07": "Horse", "GameRomHorse08": "Horse", "GameRomHorse09": "Horse", "GameRomHorse10": "Horse", "GameRomHorse11": "Horse", "GameRomHorse12": "Horse", "GameRomHorse13": "Horse", "GameRomHorse14": "Horse", "GameRomHorse15": "Horse", "GameRomHorse16": "Horse", "GameRomHorse17": "Horse", "GameRomHorse18": "Horse", "GameRomHorse19": "Horse", "GameRomHorse20": "Horse", "GameRomHorse21": "Horse", "GameRomHorse22": "Horse", "GameRomHorse23": "Horse", "GameRomHorse25": "Horse", "GameRomHorse26": "Horse", "GameRomHorseBone_AllDay": "Stalhorse", "GameRomHorseEpona": "Horse", "GameRomHorseForStreetVender": "Horse", "GameRomHorseSaddle_00S": "Stable Saddle", "GameRomHorseSaddle_00S_AncientAssistant": "Stable Saddle", "GameRomHorseSaddle_01S": "Stable Saddle", "GameRomHorseSaddle_07_ExternalCoupler": "To<PERSON>", "GameRomHorseSaddle_07_WithWagon": "To<PERSON>", "GameRomHorseSpPattern": "Horse", "HorseGodTextNPC": "<PERSON><PERSON><PERSON>", "Horse_Link_Mane_01_Reduction": "<PERSON>", "Horse_Link_Mane_Reduction": "Normal Mane", "IceWall_Piece": "Ice", "IronBall": "Iron Ball", "Item_CatchInAir": "Star Fragment", "Item_ChilledFish_01_L": "<PERSON><PERSON><PERSON> Bass", "Item_ChilledFish_03_C": "Frozen Trout", "Item_ChilledFish_03_D": "Frozen Trout", "Item_ChilledFish_03_J": "Frozen Trout", "Item_ChilledFish_04_E": "Frozen Carp", "Item_ChilledFish_04_Z": "Frozen Carp", "Item_ChilledFish_05_F": "Frozen Porgy", "Item_ChilledFish_07_K": "<PERSON><PERSON><PERSON>", "Item_ChilledFish_07_O": "<PERSON><PERSON><PERSON>", "Item_Enemy_130_Bundle_A": "Zonai Charge", "Item_Enemy_130_Bundle_B": "Zonai Charge", "Item_Enemy_130_forDisplay": "Zonai Charge", "Item_Enemy_131_forDisplay": "Large Zonai Charge", "Item_Enemy_155_Bundle_A": "Gleeok Thunder Horn", "Item_Enemy_156_Bundle_A": "Gleeok Wing", "Item_Enemy_32_Bundle_A": "Hinox Toenail", "Item_RoastFish_01_L": "Roasted Bass", "Item_RoastFish_03_C": "Roasted Trout", "Item_RoastFish_03_D": "Roasted Trout", "Item_RoastFish_03_J": "Roasted Trout", "Item_RoastFish_07_E": "Roasted Carp", "Item_RoastFish_07_Z": "Roasted Carp", "Item_RoastFish_09_F": "Roasted Porgy", "Item_RoastFish_15_K": "Blackened Crab", "Item_RoastFish_15_O": "Blackened Crab", "KibakoDesert_Contain_01": "Wooden Box", "KibakoDungeon": "Wooden Box", "KibakoGerudo_Contain_01": "Wooden Box", "KibakoJungle_Contain_01": "Wooden Box", "KibakoSeaside_Contain_01": "Wooden Box", "KibakoSky_Contain_01": "Wooden Box", "KibakoSnowMountain_Contain_01": "Wooden Box", "KibakoVolcano_Contain_01": "Wooden Box", "KibakoZora_Contain_01": "Wooden Box", "LightBall_Large_NoReaction": "Giant Brightbloom Seed", "LightBall_Large_Pile_A_01": "Giant Brightbloom Seed", "LightBall_Large_Pile_A_02": "Giant Brightbloom Seed", "LightBall_Small_NoReaction": "Bright<PERSON><PERSON> Seed", "LightBall_Small_Pile_A_01": "Bright<PERSON><PERSON> Seed", "LightBall_Small_Pile_A_02": "Bright<PERSON><PERSON> Seed", "LikeLikeRock": "Boulder", "LikeLikeRock_ForAttachment": "Boulder", "MiasmaSwarm": "Phantom Ganon", "MiasmaSwarm_DekuTree": "Phantom Ganon", "MiasmaSwarm_LastDungeon": "Phantom Ganon", "MiniGame_Basketball_Ball": "<PERSON>", "MiniGame_Basketball_Ball_HighScore": "<PERSON>", "MinusObj_TreeGeneralleaf_A_01_Treant_ForAttachment": "Evermean Log", "MinusObj_TreeGeneralleaf_A_01_Trent_Trunk": "Evermean Log", "MinusObj_TreeGeneralleaf_A_01_Trunk": "Log", "MinusObj_TreeGeneralleaf_A_01_Trunk_Aligned": "Log", "MinusObj_TreeGeneralleaf_B_01_Trunk": "Log", "MinusObj_TreeGeneralleaf_B_01_Trunk_Aligned": "Log", "MinusObj_TreeGeneralleaf_C_01_Trunk": "Log", "MinusObj_TreeGeneralleaf_C_01_Trunk_Aligned": "Log", "MinusObj_TreeGeneralleaf_D_01_Trunk": "Log", "MinusObj_TreeGeneralleaf_D_01_Trunk_Aligned": "Log", "MinusObj_UDSpot_StoneBoard_B_01": "<PERSON> Slab", "NPC_Item_Plant_M": "<PERSON>", "NPC_Item_Roast_24": "Roasted Swift Carrot", "NPC_artist_000": "Pikango", "Npc_Assassin_Zelda": "<PERSON><PERSON><PERSON>", "Npc_Attacked_008_Road": "Nat", "Npc_Attacked_009_Road": "<PERSON><PERSON><PERSON>", "Npc_Cannon03_02": "<PERSON><PERSON>", "Npc_Cannon04_01": "<PERSON><PERSON>", "Npc_Cannon04_02": "<PERSON><PERSON>", "Npc_Cannon05_01": "<PERSON><PERSON>", "Npc_Cannon05_02": "<PERSON><PERSON>", "Npc_Cannon06_02": "<PERSON><PERSON>", "Npc_Cannon06_03": "<PERSON><PERSON>", "Npc_Cannon07_02": "<PERSON><PERSON>", "Npc_Cannon08_02": "<PERSON><PERSON>", "Npc_Cannon09_01": "<PERSON><PERSON>", "Npc_Cannon09_01_AfterRepair": "<PERSON><PERSON>", "Npc_Cannon10_02": "<PERSON><PERSON>", "Npc_Cannon11_02": "<PERSON><PERSON>", "Npc_Cannon12_02": "<PERSON><PERSON>", "Npc_Cannon12_03": "<PERSON><PERSON>", "Npc_Cannon13_02": "<PERSON><PERSON>", "Npc_Cannon13_03": "<PERSON><PERSON>", "Npc_Cannon14_02": "<PERSON><PERSON>", "Npc_Cannon15_02": "<PERSON><PERSON>", "Npc_Cannon_01_Pants": "<PERSON><PERSON>", "Npc_Cannon_02": "<PERSON><PERSON>", "Npc_CraftSignboard_Complete": "<PERSON>", "Npc_DeathEastHatago_002_HotSpring": "<PERSON><PERSON>", "Npc_DeathMt001_SnowfieldHatago": "<PERSON><PERSON>", "Npc_FakeZelda_Assassin": "<PERSON><PERSON><PERSON>", "Npc_FaronWoods006_Road": "<PERSON><PERSON><PERSON>", "Npc_FirstColony004_RiverSide": "Dai", "Npc_Gaman01": "<PERSON>ge", "Npc_Gaman02": "<PERSON><PERSON><PERSON>", "Npc_Gaman03": "<PERSON><PERSON><PERSON>", "Npc_Ganondorf_Human": "Demon King <PERSON>", "Npc_GerudoDesert001_Minigame": "<PERSON><PERSON><PERSON>", "Npc_Gerudo_Queen_Young": "Riju", "Npc_Goron_Yunbo": "<PERSON><PERSON><PERSON>", "Npc_Goron_Yunbo_AfterSage": "<PERSON><PERSON><PERSON>", "Npc_Goron_Yunbo_Sage": "<PERSON><PERSON><PERSON>", "Npc_Goron_Yunbo_Sage_Puppet": "<PERSON><PERSON><PERSON>", "Npc_HatenoGate002_Road": "<PERSON><PERSON>", "Npc_HorseBuyer_01_01": "Nembis", "Npc_HyruleDepthHatago006_Road": "<PERSON><PERSON>", "Npc_HyruleWestHatago002_Road": "<PERSON><PERSON><PERSON>", "Npc_MS_Hylia002_CoH": "<PERSON><PERSON>", "Npc_MS_Hylia003A_CoH": "Yu<PERSON>", "Npc_MS_Hylia003_CoH": "<PERSON><PERSON>", "Npc_MS_Hylia004A_CoH": "<PERSON><PERSON><PERSON>", "Npc_MS_Hylia004_CoH": "Flahva", "Npc_MS_Hylia005A_CoH": "<PERSON><PERSON><PERSON>", "Npc_MS_Hylia005_CoH": "<PERSON><PERSON><PERSON>", "Npc_MiniGame_Crosscountry_Road": "Konba", "Npc_NewVolcanicRoad_AncientAssistantNormal": "Sango", "Npc_Raul": "Rauru", "Npc_Raul_InDungeon": "Rauru", "Npc_RidgeRoad_AncientAssistant_StableHostel": "Le<PERSON>", "Npc_Road_011_Road": "Kyra", "Npc_Road_013_01": "<PERSON><PERSON><PERSON>", "Npc_Road_013_02": "<PERSON><PERSON><PERSON>", "Npc_Road_013_04": "<PERSON><PERSON><PERSON>", "Npc_Road_013_05": "<PERSON><PERSON><PERSON>", "Npc_Road_013_06": "<PERSON><PERSON><PERSON>", "Npc_Road_013_07": "<PERSON><PERSON><PERSON>", "Npc_Road_013_08": "<PERSON><PERSON><PERSON>", "Npc_Road_013_09": "<PERSON><PERSON><PERSON>", "Npc_Road_013_10": "<PERSON><PERSON><PERSON>", "Npc_Road_015_Road": "<PERSON><PERSON>", "Npc_Road_026_Road": "Spoone", "Npc_Road_037_Traveller": "Falmark", "Npc_Road_041_Road": "Pitar", "Npc_Road_045_Road": "<PERSON><PERSON><PERSON>", "Npc_Tulin_Sage": "<PERSON><PERSON>", "Npc_UMiiVillage031": "<PERSON>", "Npc_VolcanicRoad_AncientAssistant_StableHostel": "<PERSON><PERSON><PERSON>", "Npc_Zelda_Opening": "<PERSON><PERSON><PERSON>", "Npc_Zonau_Golem_Servant_MainField0001": "Steward Construct", "Npc_Zonau_RaumiGolem": "Mineru", "Npc_Zora_Prince_Sage": "Sidon", "Obj_ArmorSpike_A": "<PERSON><PERSON>", "Obj_ArmorSpike_B": "<PERSON><PERSON>", "Obj_ArrowNormal_A_01": "Arrow", "Obj_BarrelOld_A_01": "Barrel", "Obj_BoardIron_A_01": "Metal Plate", "Obj_BoxIron_B_M_01": "Iron Box", "Obj_BreakBoxIron": "Iron Box", "Obj_Cushion": "Ruby Circlet", "Obj_DesertRuinTomb_A_01": "<PERSON> Slab", "Obj_DiaryAssassin_A_02": "Yiga Clan Journal", "Obj_DiaryAssassin_A_03": "Yiga Clan Journal", "Obj_DiaryAssassin_A_05": "Yiga Clan Journal", "Obj_DiaryTamul_A_06": "<PERSON><PERSON><PERSON>'s Diary", "Obj_DiaryTamul_A_07": "<PERSON><PERSON><PERSON>'s Diary", "Obj_DiaryTamul_A_08": "<PERSON><PERSON><PERSON>'s Diary", "Obj_EnemyLookoutBanana_A_01_ForAttachment": "Log", "Obj_FallFloor_A_01": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_02": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_03": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_04": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_05": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_06": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_07": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_10": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_11": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_12": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_13": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_14": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_A_15": "<PERSON><PERSON><PERSON>", "Obj_FallFloor_HyruleCastle_A_01": "<PERSON>", "Obj_FallFloor_HyruleCastle_A_02": "<PERSON>", "Obj_FallFloor_HyruleCastle_A_03": "<PERSON>", "Obj_FallFloor_HyruleCastle_A_04": "<PERSON>", "Obj_FallFloor_HyruleCastle_B_02": "<PERSON>", "Obj_FreezeBoard_A_01": "Ice", "Obj_FreezeBoard_A_02": "Ice", "Obj_GerudoHoleCover_A_01": "<PERSON> Slab", "Obj_GerudoHoleCover_A_02": "Desk", "Obj_HardLavaBlock_A_01": "<PERSON><PERSON>", "Obj_HardLavaBlock_A_02": "<PERSON><PERSON>", "Obj_Head_024": "Diamond Circlet", "Obj_Head_025": "Ruby Circlet", "Obj_Head_026": "Sapphire Circlet", "Obj_Head_027": "Topaz Earrings", "Obj_Head_028": "Opal Earrings", "Obj_Head_029": "<PERSON>", "Obj_LumberedTreeBase_Trunk": "Log", "Obj_MeatRock_Miasma_A_01": "Marbled Rock Roast", "Obj_MineralBury_A_01": "Ore Deposit", "Obj_Mineral_B_02": "Rare Ore Deposit", "Obj_Notepad_Tips_A_02": "Note on the Table", "Obj_Notepad_Tips_A_03": "Note on the Table", "Obj_Notepad_Tips_A_04": "Note on the Table", "Obj_ScaffoldWood_A_01_ForAttachment": "Log", "Obj_SheikerWakkaCharm_A_02": "<PERSON>", "Obj_SheikerWakkaCharm_A_03": "<PERSON>", "Obj_ShieldFenceWood_A_M_01": "Barricade", "Obj_ShieldFenceWood_A_M_02": "Barricade", "Obj_ShieldFenceWood_A_M_03": "Barricade", "Obj_SkyFallenBox_A_01": "<PERSON><PERSON><PERSON>", "Obj_SkyFallenBox_A_02": "<PERSON><PERSON><PERSON>", "Obj_SkyFallenBox_A_03": "<PERSON><PERSON><PERSON>", "Obj_TreeApple_A_L_01_Trunk": "Log", "Obj_TreeApple_A_L_01_Trunk_Aligned": "Log", "Obj_TreeApple_A_M_01_Treant_Trunk": "Evermean Log", "Obj_TreeApple_A_M_01_Trunk": "Log", "Obj_TreeBanana_A_01_Trunk": "Log", "Obj_TreeBroadleafDead_B_L_01_Trunk": "Log", "Obj_TreeBroadleaf_A_L_Treant_Trunk": "Evermean Log", "Obj_TreeBroadleaf_A_L_Trunk": "Log", "Obj_TreeBroadleaf_A_L_Trunk_Aligned": "Log", "Obj_TreeBurned_A_01_Trunk": "Log", "Obj_TreeBurned_A_01_Trunk_Aligned": "Log", "Obj_TreeBurned_B_01_Trunk": "Log", "Obj_TreeBurned_B_02_Trunk": "Log", "Obj_TreeCherry_A_01_CentralHyrule": "Cherry-blossom tree", "Obj_TreeConiferousDead_A_01_Trunk": "Log", "Obj_TreeConiferousDead_A_01_Trunk_Aligned": "Log", "Obj_TreeConiferousDead_A_02_Trunk": "Log", "Obj_TreeConiferousDead_A_Snow_01_Trunk": "Log", "Obj_TreeConiferousDead_A_Snow_01_Trunk_Aligned": "Log", "Obj_TreeConiferous_A_01_ForAttachment": "Log", "Obj_TreeConiferous_A_01_Trunk": "Log", "Obj_TreeConiferous_A_01_Trunk_Aligned": "Log", "Obj_TreeConiferous_A_02_Trunk": "Log", "Obj_TreeConiferous_A_03_Trunk": "Log", "Obj_TreeConiferous_A_Snow_01_Trunk": "Log", "Obj_TreeConiferous_A_Snow_01_Trunk_Aligned": "Log", "Obj_TreeConiferous_A_Snow_02_Trunk": "Log", "Obj_TreeConiferous_A_Snow_03_Trunk": "Log", "Obj_TreeConiferous_C_01_Trunk": "Log", "Obj_TreeConiferous_C_01_Trunk_Aligned": "Log", "Obj_TreeConiferous_C_02_Trunk": "Log", "Obj_TreeConiferous_C_03_Trunk": "Log", "Obj_TreeDeadLeaf_A_01_Trunk": "Log", "Obj_TreeDead_A_01_Trunk": "Log", "Obj_TreeDead_A_Snow_01_Trunk": "Log", "Obj_TreeDragonblood_A_03_Trunk": "Log", "Obj_TreeGeneralleaf_C_01_ForAttachment": "Log", "Obj_TreeGeneralleaf_D_01_ForAttachment": "Log", "Obj_TreeGhost_A_03_Trunk": "Log", "Obj_TreeMaple_A_01_ForAttachment": "Log", "Obj_TreeMaple_A_01_Trunk": "Log", "Obj_TreeMaple_A_01_Trunk_Aligned": "Log", "Obj_TreeMaple_A_02_Trunk": "Log", "Obj_TreeMaple_B_01_Trunk": "Log", "Obj_TreeMaple_B_02_Trunk": "Log", "Obj_TreeMaple_C_01_Trunk": "Log", "Obj_TreeMaple_C_02_Trunk": "Log", "Obj_TreePalmBeach_A_01_ForAttachment": "Log", "Obj_TreePalmBeach_A_01_Trunk": "Log", "Obj_TreePalmBeach_A_01_Trunk_Aligned": "Log", "Obj_TreePalmBeach_A_02_Trunk": "Log", "Obj_TreePalm_A_01_Trunk": "Log", "Obj_TreePalm_A_02_Trunk": "Log", "Obj_TreePine_A_01_Trunk": "Log", "Obj_TreeSkyApple_A_L_01_Trunk": "Log", "Obj_TreeSkyApple_A_L_01_Trunk_Aligned": "Log", "Obj_TreeSkyApple_A_M_01_Trunk": "Log", "Obj_TreeSkyDead_A_Snow_01_Trunk": "Log", "Obj_TreeWhiteBirch_A_01_ForAttachment": "Log", "Obj_TreeWhiteBirch_A_01_Trunk": "Log", "Obj_TreeWhiteBirch_A_01_Trunk_Aligned": "Log", "Obj_TreeWhiteBirch_A_02_Trunk": "Log", "Obj_TreeWhiteBirch_A_03_Trunk": "Log", "Obj_TreeWhiteBirch_A_04_Trunk": "Log", "Obj_TreeWillow_A_01_ForAttachment": "Log", "Obj_TreeWillow_A_01_Trunk": "Log", "Obj_TreeWood_A_01_ForAttachment": "Log", "Obj_TreeWood_A_L_Treant_ForAttachment": "Evermean Log", "Obj_Village_IchikaraEnokidaCutout_A_02": "President <PERSON>", "Parasail_Event": "Paraglider", "Pot": "Pot", "RBox_Field_P": "Device Dispenser", "RockBall": "Boulder", "Sage_NormalArrow": "Arrow", "Sage_Soul_Gerudo": "Riju", "Sage_Soul_Goron": "<PERSON><PERSON><PERSON>", "Sage_Soul_Rito": "<PERSON><PERSON>", "Sage_Soul_Zonau": "Mineru", "Sage_Soul_Zora": "Sidon", "SkyObj_Pot_A_M_Act_01": "Pot", "SkyObj_Pot_A_S_Act_01": "Pot", "SkyObj_Remains_Block_Fall_A_01": "<PERSON><PERSON><PERSON>", "SkyObj_Rito_Block_Fall_A_01": "<PERSON><PERSON><PERSON>", "SoulSage_NormalArrow": "Arrow", "SpObj_BalloonEnvelope_A_03": "Balloon", "SpObj_FlashLight_A_02": "Light", "StoneBall": "<PERSON> Ball", "TBox_Dungeon_Iron": "Treasure Chest", "TBox_Dungeon_Iron_Opened": "Treasure Chest", "TBox_Dungeon_Stone": "Treasure Chest", "TBox_Dungeon_Wood": "Treasure Chest", "TBox_Dungeon_Wood_Opened": "Treasure Chest", "TBox_Field_Gamble": "Treasure Chest", "TBox_Field_Iron_NoReaction": "Treasure Chest", "TBox_Field_Iron_Sandworm": "Treasure Chest", "TBox_Field_Stone_NoReaction": "Treasure Chest", "TBox_Field_Wood_Opened": "Treasure Chest", "TBox_Field_Wood_Zonau_Opened": "Treasure Chest", "TimerBarrelBomb": "Time Bomb", "TwnObj_City_GerudoPot_A_LL_Act_02": "Pot", "TwnObj_City_GerudoPot_A_M_Act_01": "Pot", "TwnObj_City_GerudoPot_A_S_Act_01": "Pot", "TwnObj_City_GerudoWoodBox_B_01": "Wooden Box", "TwnObj_City_GoronPot_A_M_Act_01": "Pot", "TwnObj_FairySpringClose_E_01": "<PERSON><PERSON><PERSON>", "TwnObj_GerudoDollBlue_A_02_CollectObject": "Blue-Seal Toy", "TwnObj_GerudoDollSmallGreen_A_01_CollectObject": "Green-Seal Toy", "TwnObj_GerudoDollSmallGreen_A_01_FindSunaNui": "Green-Seal Toy", "TwnObj_GerudoDollSmallRed_A_01_CollectObject": "Red-Seal Toy", "TwnObj_GerudoUndergroundStatue_A_10": "Monument to Seven Heroines", "TwnObj_HyruleCastleObject_Shelf_A_01": "Board", "TwnObj_SuperGoddesStatue_Down_A_01": "Forgotten Temple Statue", "TwnObj_Village_FishingPot_A_M_Act_01": "Pot", "TwnObj_Village_Fishing_Boat_A_02_ForAttachment": "Boat", "TwnObj_Village_HatenoGuidePost_A_01_Trunk": "Signboard", "TwnObj_Village_HatenoPot_A_L_Act_01": "Pot", "TwnObj_Village_HatenoPot_A_M_Act_01": "Pot", "TwnObj_Village_HatenoPot_A_S_Act_01": "Pot", "TwnObj_Village_HatenoSchoolSignboard_A_01_Trunk": "Signboard", "TwnObj_Village_KorokPot_A_S_Act_01": "Pot", "TwnObj_Village_Korok_DekuTree_A_01_Far": "Great Deku Tree", "TwnObj_Village_RitoPot_A_M_Act_01": "Pot", "TwnObj_Village_RitoPot_A_M_Act_02": "Pot", "TwnObj_Village_RitoPot_A_S_Act_01": "Pot", "TwnObj_Village_RitoPot_A_S_Act_02": "Pot", "TwnObj_Village_SheikerPot_A_LL_Act_01": "Pot", "TwnObj_Village_SheikerPot_A_L_Act_01": "Pot", "TwnObj_Village_SheikerPot_A_M_Act_02": "Pot", "TwnObj_Village_SheikerPot_A_S_Act_01": "Pot", "TwnObj_Village_ZoraPot_A_M_Act_01": "Pot", "Weapon_Bow_116": "Swallow Bow", "Weapon_Bow_128": "Great Eagle Bow", "Weapon_Bow_129": "Great Eagle Bow", "Weapon_Bow_166_For_Ganondorf": "Demon King's Bow", "Weapon_Lsword_154": "Boulder Breaker", "Weapon_Spear_036_MercenarySoldier": "<PERSON><PERSON>", "Weapon_Spear_150": "Lightscale Trident", "Weapon_Sword_071": "Master Sword", "Weapon_Sword_071_Broken": "Master Sword", "Weapon_Sword_152_Main": "Scimitar of the Seven", "Weapon_Sword_152_Sub": "Scimitar of the Seven", "Zonau_BlockMaster_Block": "Block", "Zonau_BlockMaster_Block_ForAttachment": "Block", "Zonau_BlockMaster_Block_Middle": "Block", "Zonau_BlockMaster_Block_Middle_ForAttachment": "Block", "Zonau_BlockMaster_Block_Senior": "Block", "Zonau_BlockMaster_Block_Senior_ForAttachment": "Block", "subchallnpc000_DuelPeak": "Domidak", "subchallnpc000_twin_DuelPeak": "<PERSON><PERSON><PERSON>"}